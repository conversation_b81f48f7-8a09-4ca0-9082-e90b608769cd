<template>
  <div class="modal fade" id="faturaModal" tabindex="-1" aria-labelledby="faturaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="faturaModalLabel">
            {{ isEditing ? 'Editar Fatura' : 'Nova Fatura' }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="salvarFatura">
            <div class="row">
              <div class="col-md-12">
                <material-input
                  id="descricao"
                  v-model="fatura.descricao"
                  label="Descrição da Fatura"
                  type="text"
                  :is-required="true"
                />
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <material-input
                  id="categoria"
                  v-model="fatura.categoria"
                  label="Categoria"
                  type="text"
                  placeholder="Ex: Consulta, Tratamento, Aparelho..."
                />
              </div>
              <div class="col-md-6">
                <material-input
                  id="valor_total"
                  v-model="fatura.valor_total"
                  label="Valor Total"
                  type="number"
                  step="0.01"
                  min="0"
                  :is-required="true"
                />
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="form-check form-switch">
                  <input 
                    class="form-check-input" 
                    type="checkbox" 
                    id="parcelar"
                    v-model="fatura.parcelar"
                    @change="toggleParcelamento"
                  >
                  <label class="form-check-label" for="parcelar">
                    Parcelar fatura
                  </label>
                </div>
              </div>
            </div>

            <div v-if="fatura.parcelar" class="row mt-3">
              <div class="col-md-6">
                <material-input
                  id="numero_parcelas"
                  v-model="fatura.numero_parcelas"
                  label="Número de Parcelas"
                  type="number"
                  min="2"
                  max="24"
                  :is-required="fatura.parcelar"
                  @input="calcularParcelas"
                />
              </div>
              <div class="col-md-6">
                <material-input
                  id="data_vencimento_primeira"
                  v-model="fatura.data_vencimento_primeira"
                  label="Vencimento da 1ª Parcela"
                  type="date"
                  :is-required="fatura.parcelar"
                  @input="calcularParcelas"
                />
              </div>
            </div>

            <div v-if="!fatura.parcelar" class="row">
              <div class="col-md-6">
                <material-input
                  id="data_vencimento"
                  v-model="fatura.data_vencimento"
                  label="Data de Vencimento"
                  type="date"
                  :is-required="!fatura.parcelar"
                />
              </div>
            </div>

            <!-- Preview das parcelas -->
            <div v-if="fatura.parcelar && previewParcelas.length > 0" class="mt-4">
              <h6 class="text-sm font-weight-bold">Preview das Parcelas:</h6>
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th class="text-xs">Parcela</th>
                      <th class="text-xs">Valor</th>
                      <th class="text-xs">Vencimento</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(parcela, index) in previewParcelas" :key="index">
                      <td class="text-xs">{{ index + 1 }}/{{ fatura.numero_parcelas }}</td>
                      <td class="text-xs">{{ formatarValor(parcela.valor) }}</td>
                      <td class="text-xs">{{ formatarData(parcela.data_vencimento) }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="row mt-3">
              <div class="col-md-12">
                <material-textarea
                  id="observacoes"
                  v-model="fatura.observacoes"
                  label="Observações"
                  rows="3"
                />
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <material-button
            variant="outline"
            color="secondary"
            @click="fecharModal"
          >
            Cancelar
          </material-button>
          <material-button
            variant="gradient"
            color="success"
            @click="salvarFatura"
            :disabled="isSaving"
          >
            <span v-if="isSaving" class="spinner-border spinner-border-sm me-2" role="status"></span>
            {{ isEditing ? 'Atualizar' : 'Salvar' }}
          </material-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import MaterialTextarea from "@/components/MaterialTextarea.vue";
import MaterialButton from "@/components/MaterialButton.vue";
import { 
  criarFatura, 
  atualizarFatura, 
  gerarParcelamento,
  calcularDataVencimentoInteligente,
  formatarValor,
  formatarData
} from "@/services/faturasService";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "FaturaModal",
  components: {
    MaterialInput,
    MaterialTextarea,
    MaterialButton,
  },
  props: {
    pacienteId: {
      type: Number,
      required: true
    }
  },
  emits: ['fatura-salva'],
  data() {
    return {
      isEditing: false,
      isSaving: false,
      fatura: {
        id: null,
        descricao: '',
        categoria: '',
        valor_total: '',
        data_vencimento: '',
        data_vencimento_primeira: '',
        numero_parcelas: 2,
        parcelar: false,
        observacoes: ''
      },
      previewParcelas: []
    };
  },
  mounted() {
    // Definir data padrão como hoje + 30 dias
    const hoje = new Date();
    const dataVencimentoPadrao = new Date(hoje.getTime() + (30 * 24 * 60 * 60 * 1000));
    this.fatura.data_vencimento = dataVencimentoPadrao.toISOString().split('T')[0];
    this.fatura.data_vencimento_primeira = dataVencimentoPadrao.toISOString().split('T')[0];
  },
  methods: {
    abrirModal(faturaData = null) {
      this.isEditing = !!faturaData;

      if (faturaData) {
        this.fatura = { ...faturaData };
        this.fatura.parcelar = !!faturaData.total_parcelas && faturaData.total_parcelas > 1;
        if (this.fatura.parcelar) {
          this.calcularParcelas();
        }
      } else {
        this.resetarFatura();
      }

      const modal = new bootstrap.Modal(document.getElementById('faturaModal'));
      modal.show();
    },

    fecharModal() {
      const modal = bootstrap.Modal.getInstance(document.getElementById('faturaModal'));
      modal.hide();
      this.resetarFatura();
    },

    resetarFatura() {
      // Definir data padrão como hoje + 30 dias
      const hoje = new Date();
      const dataVencimentoPadrao = new Date(hoje.getTime() + (30 * 24 * 60 * 60 * 1000));
      const dataFormatada = dataVencimentoPadrao.toISOString().split('T')[0];

      this.fatura = {
        id: null,
        descricao: '',
        categoria: '',
        valor_total: '',
        data_vencimento: dataFormatada,
        data_vencimento_primeira: dataFormatada,
        numero_parcelas: 2,
        parcelar: false,
        observacoes: ''
      };
      this.previewParcelas = [];
      this.isEditing = false;
    },

    toggleParcelamento() {
      if (!this.fatura.parcelar) {
        this.previewParcelas = [];
      } else {
        this.calcularParcelas();
      }
    },

    calcularParcelas() {
      if (!this.fatura.parcelar || !this.fatura.valor_total || !this.fatura.numero_parcelas || !this.fatura.data_vencimento_primeira) {
        this.previewParcelas = [];
        return;
      }

      const valorTotal = parseFloat(this.fatura.valor_total);
      const numeroParcelas = parseInt(this.fatura.numero_parcelas);
      
      if (valorTotal <= 0 || numeroParcelas <= 0) {
        this.previewParcelas = [];
        return;
      }

      const valorParcela = valorTotal / numeroParcelas;
      const datasVencimento = calcularDataVencimentoInteligente(this.fatura.data_vencimento_primeira, numeroParcelas);

      this.previewParcelas = datasVencimento.map((data, index) => ({
        numero: index + 1,
        valor: index === numeroParcelas - 1 ? 
          valorTotal - (valorParcela * (numeroParcelas - 1)) : // Última parcela ajusta diferenças de centavos
          valorParcela,
        data_vencimento: data
      }));
    },

    async salvarFatura() {
      if (!this.validarFormulario()) {
        return;
      }

      this.isSaving = true;
      cSwal.loading('Salvando fatura...');

      try {
        let resultado;

        if (this.fatura.parcelar) {
          // Gerar parcelamento
          const dadosParcelamento = {
            paciente_id: this.pacienteId,
            descricao: this.fatura.descricao,
            categoria: this.fatura.categoria,
            valor_total: parseFloat(this.fatura.valor_total),
            numero_parcelas: parseInt(this.fatura.numero_parcelas),
            data_vencimento_primeira: this.fatura.data_vencimento_primeira,
            observacoes: this.fatura.observacoes
          };

          resultado = await gerarParcelamento(dadosParcelamento);
        } else {
          // Fatura única
          const dadosFatura = {
            paciente_id: this.pacienteId,
            descricao: this.fatura.descricao,
            categoria: this.fatura.categoria,
            valor: parseFloat(this.fatura.valor_total),
            data_vencimento: this.fatura.data_vencimento,
            observacoes: this.fatura.observacoes
          };

          if (this.isEditing) {
            resultado = await atualizarFatura(this.fatura.id, dadosFatura);
          } else {
            resultado = await criarFatura(dadosFatura);
          }
        }

        if (resultado) {
          cSwal.loaded();
          cSwal.cSuccess(
            this.fatura.parcelar ? 
              `Parcelamento criado com sucesso! ${this.fatura.numero_parcelas} parcelas geradas.` :
              `Fatura ${this.isEditing ? 'atualizada' : 'criada'} com sucesso!`
          );
          
          this.$emit('fatura-salva');
          this.fecharModal();
        } else {
          cSwal.loaded();
          cSwal.cError('Erro ao salvar fatura. Tente novamente.');
        }
      } catch (error) {
        console.error('Erro ao salvar fatura:', error);
        cSwal.loaded();
        cSwal.cError('Erro ao salvar fatura. Tente novamente.');
      } finally {
        this.isSaving = false;
      }
    },

    validarFormulario() {
      if (!this.fatura.descricao.trim()) {
        cSwal.cError('Por favor, informe a descrição da fatura.');
        return false;
      }

      if (!this.fatura.valor_total || parseFloat(this.fatura.valor_total) <= 0) {
        cSwal.cError('Por favor, informe um valor válido para a fatura.');
        return false;
      }

      if (this.fatura.parcelar) {
        if (!this.fatura.numero_parcelas || parseInt(this.fatura.numero_parcelas) < 2) {
          cSwal.cError('Para parcelamento, informe pelo menos 2 parcelas.');
          return false;
        }

        if (!this.fatura.data_vencimento_primeira) {
          cSwal.cError('Para parcelamento, informe a data de vencimento da primeira parcela.');
          return false;
        }
      } else {
        if (!this.fatura.data_vencimento) {
          cSwal.cError('Por favor, informe a data de vencimento da fatura.');
          return false;
        }
      }

      return true;
    },

    formatarValor,
    formatarData
  }
};
</script>

<style scoped>
.modal-content {
  border-radius: 0.75rem;
  border: none;
  box-shadow: 0 20px 27px 0 rgba(0, 0, 0, 0.05);
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  padding: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  padding: 1rem 1.5rem;
}

.table-responsive {
  max-height: 200px;
  overflow-y: auto;
}

.form-check-input:checked {
  background-color: #82d616;
  border-color: #82d616;
}
</style>
