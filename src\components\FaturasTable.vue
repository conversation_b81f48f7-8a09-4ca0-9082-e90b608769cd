<template>
  <div class="card">
    <div class="card-header pb-0">
      <div class="d-flex justify-content-between align-items-center">
        <h6 class="mb-0">Faturas do Paciente</h6>
        <material-button
          variant="gradient"
          color="success"
          size="sm"
          @click="$emit('nova-fatura')"
        >
          <i class="fas fa-plus me-2"></i>
          Nova Fatura
        </material-button>
      </div>
    </div>
    <div class="card-body px-0 pt-0 pb-2">
      <div v-if="isLoading" class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Carregando...</span>
        </div>
      </div>
      
      <div v-else-if="faturas.length === 0" class="text-center py-4">
        <i class="fas fa-file-invoice-dollar fa-3x text-muted mb-3"></i>
        <p class="text-muted">Nenhuma fatura encontrada para este paciente.</p>
        <material-button
          variant="gradient"
          color="success"
          size="sm"
          @click="$emit('nova-fatura')"
        >
          <i class="fas fa-plus me-2"></i>
          Criar primeira fatura
        </material-button>
      </div>

      <div v-else class="table-responsive p-0">
        <table class="table align-items-center mb-0">
          <thead>
            <tr>
              <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                Descrição
              </th>
              <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">
                Valor
              </th>
              <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                Vencimento
              </th>
              <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                Status
              </th>
              <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                Parcela
              </th>
              <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                Ações
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="fatura in faturas" :key="fatura.id">
              <td>
                <div class="d-flex px-2 py-1">
                  <div class="d-flex flex-column justify-content-center">
                    <h6 class="mb-0 text-sm">{{ fatura.descricao }}</h6>
                    <p class="text-xs text-secondary mb-0" v-if="fatura.categoria">
                      {{ fatura.categoria }}
                    </p>
                  </div>
                </div>
              </td>
              <td>
                <p class="text-xs font-weight-bold mb-0">{{ formatarValor(fatura.valor) }}</p>
                <p class="text-xs text-secondary mb-0" v-if="fatura.valor_pago > 0">
                  Pago: {{ formatarValor(fatura.valor_pago) }}
                </p>
              </td>
              <td class="align-middle text-center text-sm">
                <span class="text-secondary text-xs font-weight-bold">
                  {{ formatarData(fatura.data_vencimento) }}
                </span>
              </td>
              <td class="align-middle text-center">
                <span 
                  class="badge badge-sm"
                  :class="getStatusFatura(fatura.status).class"
                >
                  {{ getStatusFatura(fatura.status).label }}
                </span>
              </td>
              <td class="align-middle text-center">
                <span class="text-secondary text-xs font-weight-bold" v-if="fatura.parcela_numero">
                  {{ fatura.parcela_numero }}/{{ fatura.total_parcelas }}
                </span>
                <span class="text-secondary text-xs" v-else>
                  Única
                </span>
              </td>
              <td class="align-middle text-center">
                <div class="dropdown">
                  <button 
                    class="btn btn-link text-secondary mb-0" 
                    type="button" 
                    :id="`dropdownMenuButton${fatura.id}`"
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                  >
                    <i class="fa fa-ellipsis-v text-xs"></i>
                  </button>
                  <ul class="dropdown-menu" :aria-labelledby="`dropdownMenuButton${fatura.id}`">
                    <li>
                      <a 
                        class="dropdown-item" 
                        href="#" 
                        @click.prevent="$emit('editar-fatura', fatura)"
                      >
                        <i class="fas fa-edit me-2"></i>
                        Editar
                      </a>
                    </li>
                    <li v-if="fatura.status === 'pendente'">
                      <a 
                        class="dropdown-item" 
                        href="#" 
                        @click.prevent="$emit('marcar-paga', fatura)"
                      >
                        <i class="fas fa-check me-2"></i>
                        Marcar como Paga
                      </a>
                    </li>
                    <li v-if="fatura.status !== 'cancelada'">
                      <a 
                        class="dropdown-item text-danger" 
                        href="#" 
                        @click.prevent="$emit('cancelar-fatura', fatura)"
                      >
                        <i class="fas fa-ban me-2"></i>
                        Cancelar
                      </a>
                    </li>
                    <li>
                      <hr class="dropdown-divider">
                    </li>
                    <li>
                      <a 
                        class="dropdown-item text-danger" 
                        href="#" 
                        @click.prevent="$emit('excluir-fatura', fatura)"
                      >
                        <i class="fas fa-trash me-2"></i>
                        Excluir
                      </a>
                    </li>
                  </ul>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Resumo financeiro -->
      <div v-if="faturas.length > 0" class="row mt-4 px-3">
        <div class="col-md-3">
          <div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
            <i class="fas fa-coins text-success text-lg me-3"></i>
            <div>
              <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Faturado</p>
              <h5 class="font-weight-bolder mb-0">
                {{ formatarValor(totalFaturado) }}
              </h5>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
            <i class="fas fa-check-circle text-success text-lg me-3"></i>
            <div>
              <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Pago</p>
              <h5 class="font-weight-bolder mb-0">
                {{ formatarValor(totalPago) }}
              </h5>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
            <i class="fas fa-clock text-warning text-lg me-3"></i>
            <div>
              <p class="text-sm mb-0 text-capitalize font-weight-bold">Em Aberto</p>
              <h5 class="font-weight-bolder mb-0">
                {{ formatarValor(totalEmAberto) }}
              </h5>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
            <i class="fas fa-exclamation-triangle text-danger text-lg me-3"></i>
            <div>
              <p class="text-sm mb-0 text-capitalize font-weight-bold">Vencidas</p>
              <h5 class="font-weight-bolder mb-0">
                {{ formatarValor(totalVencidas) }}
              </h5>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MaterialButton from "@/components/MaterialButton.vue";
import { formatarValor, formatarData, getStatusFatura } from "@/services/faturasService";

export default {
  name: "FaturasTable",
  components: {
    MaterialButton,
  },
  props: {
    faturas: {
      type: Array,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'nova-fatura',
    'editar-fatura', 
    'excluir-fatura',
    'marcar-paga',
    'cancelar-fatura'
  ],
  computed: {
    totalFaturado() {
      return this.faturas.reduce((total, fatura) => total + parseFloat(fatura.valor || 0), 0);
    },
    totalPago() {
      return this.faturas.reduce((total, fatura) => total + parseFloat(fatura.valor_pago || 0), 0);
    },
    totalEmAberto() {
      return this.faturas
        .filter(fatura => ['pendente', 'parcial'].includes(fatura.status))
        .reduce((total, fatura) => total + (parseFloat(fatura.valor || 0) - parseFloat(fatura.valor_pago || 0)), 0);
    },
    totalVencidas() {
      return this.faturas
        .filter(fatura => fatura.status === 'vencida')
        .reduce((total, fatura) => total + (parseFloat(fatura.valor || 0) - parseFloat(fatura.valor_pago || 0)), 0);
    }
  },
  methods: {
    formatarValor,
    formatarData,
    getStatusFatura
  }
};
</script>

<style scoped>
.table th {
  border-bottom: 1px solid #e9ecef;
}

.table td {
  border-bottom: 1px solid #f8f9fa;
}

.dropdown-menu {
  border: 0;
  box-shadow: 0 0.25rem 0.375rem -0.0625rem rgba(20, 20, 20, 0.12), 0 0.125rem 0.25rem -0.0625rem rgba(20, 20, 20, 0.07);
}

.badge {
  font-size: 0.75rem;
}
</style>
