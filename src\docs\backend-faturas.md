# Sistema de Faturas - Documentação do Back-end

## Visão Geral

Este documento descreve a estrutura necessária no back-end Laravel para implementar o sistema de faturas de pacientes com funcionalidade de parcelamento inteligente.

## Migration - Tabela `faturas`

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFaturasTable extends Migration
{
    public function up()
    {
        Schema::create('faturas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('paciente_id')->constrained('pacientes')->onDelete('cascade');
            $table->string('descricao');
            $table->string('categoria')->nullable();
            $table->decimal('valor', 10, 2);
            $table->decimal('valor_pago', 10, 2)->default(0);
            $table->date('data_vencimento');
            $table->date('data_pagamento')->nullable();
            $table->enum('status', ['pendente', 'paga', 'vencida', 'cancelada', 'parcial'])->default('pendente');
            $table->string('forma_pagamento')->nullable();
            $table->text('observacoes')->nullable();
            $table->text('motivo_cancelamento')->nullable();
            
            // Campos para parcelamento
            $table->integer('parcela_numero')->nullable();
            $table->integer('total_parcelas')->nullable();
            $table->foreignId('fatura_pai_id')->nullable()->constrained('faturas')->onDelete('cascade');
            
            $table->timestamps();
            
            // Índices
            $table->index(['paciente_id', 'status']);
            $table->index(['data_vencimento', 'status']);
            $table->index('fatura_pai_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('faturas');
    }
}
```

## Model - `Fatura`

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Fatura extends Model
{
    use HasFactory;

    protected $fillable = [
        'paciente_id',
        'descricao',
        'categoria',
        'valor',
        'valor_pago',
        'data_vencimento',
        'data_pagamento',
        'status',
        'forma_pagamento',
        'observacoes',
        'motivo_cancelamento',
        'parcela_numero',
        'total_parcelas',
        'fatura_pai_id'
    ];

    protected $casts = [
        'data_vencimento' => 'date',
        'data_pagamento' => 'date',
        'valor' => 'decimal:2',
        'valor_pago' => 'decimal:2'
    ];

    // Relacionamentos
    public function paciente()
    {
        return $this->belongsTo(Paciente::class);
    }

    public function faturaPai()
    {
        return $this->belongsTo(Fatura::class, 'fatura_pai_id');
    }

    public function parcelas()
    {
        return $this->hasMany(Fatura::class, 'fatura_pai_id');
    }

    // Scopes
    public function scopePendentes($query)
    {
        return $query->where('status', 'pendente');
    }

    public function scopeVencidas($query)
    {
        return $query->where('status', 'vencida')
                    ->orWhere(function($q) {
                        $q->where('status', 'pendente')
                          ->where('data_vencimento', '<', Carbon::today());
                    });
    }

    public function scopePagas($query)
    {
        return $query->where('status', 'paga');
    }

    // Mutators e Accessors
    public function getValorRestanteAttribute()
    {
        return $this->valor - $this->valor_pago;
    }

    public function getEstaVencidaAttribute()
    {
        return $this->status === 'pendente' && $this->data_vencimento < Carbon::today();
    }

    // Métodos
    public function marcarComoPaga($dataPagamento = null, $formaPagamento = null, $observacoes = null)
    {
        $this->update([
            'status' => 'paga',
            'valor_pago' => $this->valor,
            'data_pagamento' => $dataPagamento ?: Carbon::today(),
            'forma_pagamento' => $formaPagamento,
            'observacoes' => $observacoes
        ]);
    }

    public function cancelar($motivo)
    {
        $this->update([
            'status' => 'cancelada',
            'motivo_cancelamento' => $motivo
        ]);
    }

    public function atualizarStatus()
    {
        if ($this->status === 'pendente' && $this->data_vencimento < Carbon::today()) {
            $this->update(['status' => 'vencida']);
        }
    }

    // Boot method para atualizar status automaticamente
    protected static function boot()
    {
        parent::boot();

        static::retrieved(function ($fatura) {
            $fatura->atualizarStatus();
        });
    }
}
```

## Controller - `FaturaController`

```php
<?php

namespace App\Http\Controllers;

use App\Models\Fatura;
use App\Models\Paciente;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FaturaController extends Controller
{
    public function index(Request $request)
    {
        $query = Fatura::with('paciente');

        if ($request->has('paciente_id')) {
            $query->where('paciente_id', $request->paciente_id);
        }

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $faturas = $query->orderBy('data_vencimento', 'desc')->get();

        return response()->json([
            'status' => 'success',
            'data' => $faturas
        ]);
    }

    public function show(Fatura $fatura)
    {
        $fatura->load('paciente', 'parcelas');
        
        return response()->json([
            'status' => 'success',
            'data' => $fatura
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'paciente_id' => 'required|exists:pacientes,id',
            'descricao' => 'required|string|max:255',
            'categoria' => 'nullable|string|max:100',
            'valor' => 'required|numeric|min:0.01',
            'data_vencimento' => 'required|date',
            'observacoes' => 'nullable|string'
        ]);

        $fatura = Fatura::create($request->all());

        return response()->json([
            'status' => 'success',
            'data' => $fatura,
            'message' => 'Fatura criada com sucesso'
        ], 201);
    }

    public function update(Request $request, Fatura $fatura)
    {
        $request->validate([
            'descricao' => 'sometimes|required|string|max:255',
            'categoria' => 'nullable|string|max:100',
            'valor' => 'sometimes|required|numeric|min:0.01',
            'data_vencimento' => 'sometimes|required|date',
            'observacoes' => 'nullable|string'
        ]);

        $fatura->update($request->all());

        return response()->json([
            'status' => 'success',
            'data' => $fatura,
            'message' => 'Fatura atualizada com sucesso'
        ]);
    }

    public function destroy(Fatura $fatura)
    {
        // Se for uma fatura pai, excluir todas as parcelas
        if ($fatura->parcelas()->count() > 0) {
            $fatura->parcelas()->delete();
        }

        $fatura->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Fatura excluída com sucesso'
        ]);
    }

    public function gerarParcelamento(Request $request)
    {
        $request->validate([
            'paciente_id' => 'required|exists:pacientes,id',
            'descricao' => 'required|string|max:255',
            'categoria' => 'nullable|string|max:100',
            'valor_total' => 'required|numeric|min:0.01',
            'numero_parcelas' => 'required|integer|min:2|max:24',
            'data_vencimento_primeira' => 'required|date',
            'observacoes' => 'nullable|string'
        ]);

        DB::beginTransaction();

        try {
            $valorParcela = $request->valor_total / $request->numero_parcelas;
            $dataBase = Carbon::parse($request->data_vencimento_primeira);
            $parcelas = [];

            for ($i = 0; $i < $request->numero_parcelas; $i++) {
                $dataVencimento = $this->calcularDataVencimentoInteligente($dataBase, $i);
                
                // Ajustar a última parcela para compensar diferenças de centavos
                $valor = ($i === $request->numero_parcelas - 1) 
                    ? $request->valor_total - ($valorParcela * ($request->numero_parcelas - 1))
                    : $valorParcela;

                $parcela = Fatura::create([
                    'paciente_id' => $request->paciente_id,
                    'descricao' => $request->descricao . " - Parcela " . ($i + 1) . "/" . $request->numero_parcelas,
                    'categoria' => $request->categoria,
                    'valor' => $valor,
                    'data_vencimento' => $dataVencimento,
                    'observacoes' => $request->observacoes,
                    'parcela_numero' => $i + 1,
                    'total_parcelas' => $request->numero_parcelas
                ]);

                $parcelas[] = $parcela;
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'data' => $parcelas,
                'message' => 'Parcelamento gerado com sucesso'
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'status' => 'error',
                'message' => 'Erro ao gerar parcelamento: ' . $e->getMessage()
            ], 500);
        }
    }

    public function marcarPaga(Request $request, Fatura $fatura)
    {
        $request->validate([
            'data_pagamento' => 'required|date',
            'forma_pagamento' => 'required|string',
            'observacoes' => 'nullable|string'
        ]);

        $fatura->marcarComoPaga(
            $request->data_pagamento,
            $request->forma_pagamento,
            $request->observacoes
        );

        return response()->json([
            'status' => 'success',
            'data' => $fatura->fresh(),
            'message' => 'Fatura marcada como paga'
        ]);
    }

    public function cancelar(Request $request, Fatura $fatura)
    {
        $request->validate([
            'motivo' => 'required|string'
        ]);

        $fatura->cancelar($request->motivo);

        return response()->json([
            'status' => 'success',
            'data' => $fatura->fresh(),
            'message' => 'Fatura cancelada com sucesso'
        ]);
    }

    public function faturasPorPaciente(Paciente $paciente)
    {
        $faturas = $paciente->faturas()
            ->orderBy('data_vencimento', 'desc')
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $faturas
        ]);
    }

    private function calcularDataVencimentoInteligente(Carbon $dataBase, int $mesesAFrente)
    {
        $diaVencimento = $dataBase->day;
        $novaData = $dataBase->copy()->addMonths($mesesAFrente);
        
        // Obter o último dia do mês
        $ultimoDiaDoMes = $novaData->copy()->endOfMonth()->day;
        
        // Se o dia de vencimento é maior que o último dia do mês, usar o último dia
        $diaFinal = min($diaVencimento, $ultimoDiaDoMes);
        
        return $novaData->day($diaFinal);
    }
}
```

## Rotas - `routes/api.php`

```php
// Rotas para faturas
Route::group(['prefix' => 'faturas'], function () {
    Route::get('/', [FaturaController::class, 'index']);
    Route::post('/', [FaturaController::class, 'store']);
    Route::post('/gerar-parcelamento', [FaturaController::class, 'gerarParcelamento']);
    Route::get('/{fatura}', [FaturaController::class, 'show']);
    Route::put('/{fatura}', [FaturaController::class, 'update']);
    Route::delete('/{fatura}', [FaturaController::class, 'destroy']);
    Route::post('/{fatura}/marcar-paga', [FaturaController::class, 'marcarPaga']);
    Route::post('/{fatura}/cancelar', [FaturaController::class, 'cancelar']);
});

// Rota para faturas por paciente
Route::get('/pacientes/{paciente}/faturas', [FaturaController::class, 'faturasPorPaciente']);
```

## Atualização do Model `Paciente`

Adicionar o relacionamento com faturas no model `Paciente`:

```php
// No model App\Models\Paciente.php

/**
 * Relacionamento com faturas
 */
public function faturas()
{
    return $this->hasMany(Fatura::class);
}

/**
 * Faturas pendentes
 */
public function faturasPendentes()
{
    return $this->faturas()->where('status', 'pendente');
}

/**
 * Faturas vencidas
 */
public function faturasVencidas()
{
    return $this->faturas()->where('status', 'vencida')
                           ->orWhere(function($q) {
                               $q->where('status', 'pendente')
                                 ->where('data_vencimento', '<', now());
                           });
}

/**
 * Total em aberto (pendente + vencido)
 */
public function getTotalEmAbertoAttribute()
{
    return $this->faturas()
                ->whereIn('status', ['pendente', 'vencida', 'parcial'])
                ->sum(DB::raw('valor - valor_pago'));
}

/**
 * Total pago
 */
public function getTotalPagoAttribute()
{
    return $this->faturas()->sum('valor_pago');
}
```

## Seeder (Opcional)

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Fatura;
use App\Models\Paciente;
use Carbon\Carbon;

class FaturaSeeder extends Seeder
{
    public function run()
    {
        $pacientes = Paciente::all();

        foreach ($pacientes->take(5) as $paciente) {
            // Criar algumas faturas de exemplo
            Fatura::create([
                'paciente_id' => $paciente->id,
                'descricao' => 'Consulta de avaliação',
                'categoria' => 'Consulta',
                'valor' => 150.00,
                'data_vencimento' => Carbon::now()->addDays(30),
                'status' => 'pendente'
            ]);

            Fatura::create([
                'paciente_id' => $paciente->id,
                'descricao' => 'Aparelho ortodôntico',
                'categoria' => 'Tratamento',
                'valor' => 2500.00,
                'data_vencimento' => Carbon::now()->subDays(5),
                'status' => 'vencida'
            ]);

            Fatura::create([
                'paciente_id' => $paciente->id,
                'descricao' => 'Manutenção mensal',
                'categoria' => 'Manutenção',
                'valor' => 200.00,
                'valor_pago' => 200.00,
                'data_vencimento' => Carbon::now()->subDays(15),
                'data_pagamento' => Carbon::now()->subDays(10),
                'status' => 'paga',
                'forma_pagamento' => 'cartao_credito'
            ]);
        }
    }
}
```

## Comandos Artisan para Executar

```bash
# Criar e executar a migration
php artisan make:migration create_faturas_table
php artisan migrate

# Criar o model (se não existir)
php artisan make:model Fatura

# Criar o controller
php artisan make:controller FaturaController

# Criar o seeder (opcional)
php artisan make:seeder FaturaSeeder
php artisan db:seed --class=FaturaSeeder
```

## Observações Importantes

1. **Parcelamento Inteligente**: O sistema calcula automaticamente as datas de vencimento considerando meses com diferentes números de dias.

2. **Status Automático**: As faturas têm seu status atualizado automaticamente para "vencida" quando a data de vencimento passa.

3. **Relacionamentos**: Suporte a parcelamento com relacionamento pai-filho entre faturas.

4. **Validações**: Todas as rotas incluem validações apropriadas.

5. **Transações**: O parcelamento usa transações de banco para garantir consistência.

6. **Soft Delete**: Considere adicionar soft delete se necessário para manter histórico.

Esta estrutura fornece uma base sólida para o sistema de faturas com todas as funcionalidades solicitadas.
```
