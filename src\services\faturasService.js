import axios from '@/services/axios'

/**
 * Buscar faturas de um paciente
 * @param {number} pacienteId - ID do paciente
 * @returns {Array|false} - Lista de faturas ou false em caso de erro
 */
export async function getFaturasByPaciente(pacienteId) {
    try {
        const response = await axios.get(`/pacientes/${pacienteId}/faturas`);

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar faturas do paciente:', error);
    }

    return false;
}

/**
 * Buscar uma fatura específica
 * @param {number} faturaId - ID da fatura
 * @returns {Object|false} - Dados da fatura ou false em caso de erro
 */
export async function getFatura(faturaId) {
    try {
        const response = await axios.get(`/faturas/${faturaId}`);

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar fatura:', error);
    }

    return false;
}

/**
 * Criar uma nova fatura
 * @param {Object} faturaData - Dados da fatura
 * @returns {Object|false} - Dados da fatura criada ou false em caso de erro
 */
export async function criarFatura(faturaData) {
    try {
        const response = await axios.post('/faturas', faturaData);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data.data;

    } catch (error) {
        console.error('Erro ao criar fatura:', error);
    }

    return false;
}

/**
 * Atualizar uma fatura existente
 * @param {number} faturaId - ID da fatura
 * @param {Object} faturaData - Dados atualizados da fatura
 * @returns {Object|false} - Dados da fatura atualizada ou false em caso de erro
 */
export async function atualizarFatura(faturaId, faturaData) {
    try {
        const response = await axios.put(`/faturas/${faturaId}`, faturaData);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data.data;

    } catch (error) {
        console.error('Erro ao atualizar fatura:', error);
    }

    return false;
}

/**
 * Excluir uma fatura
 * @param {number} faturaId - ID da fatura
 * @returns {boolean} - true se excluída com sucesso, false caso contrário
 */
export async function excluirFatura(faturaId) {
    try {
        const response = await axios.delete(`/faturas/${faturaId}`);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return true;

    } catch (error) {
        console.error('Erro ao excluir fatura:', error);
    }

    return false;
}

/**
 * Gerar parcelamento inteligente
 * @param {Object} dadosParcelamento - Dados para gerar o parcelamento
 * @param {number} dadosParcelamento.paciente_id - ID do paciente
 * @param {string} dadosParcelamento.descricao - Descrição da fatura
 * @param {number} dadosParcelamento.valor_total - Valor total a ser parcelado
 * @param {number} dadosParcelamento.numero_parcelas - Número de parcelas
 * @param {string} dadosParcelamento.data_vencimento_primeira - Data de vencimento da primeira parcela (YYYY-MM-DD)
 * @param {string} dadosParcelamento.categoria - Categoria da fatura
 * @returns {Array|false} - Lista de parcelas geradas ou false em caso de erro
 */
export async function gerarParcelamento(dadosParcelamento) {
    try {
        const response = await axios.post('/faturas/gerar-parcelamento', dadosParcelamento);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data.data;

    } catch (error) {
        console.error('Erro ao gerar parcelamento:', error);
    }

    return false;
}

/**
 * Marcar fatura como paga
 * @param {number} faturaId - ID da fatura
 * @param {Object} dadosPagamento - Dados do pagamento
 * @param {string} dadosPagamento.data_pagamento - Data do pagamento (YYYY-MM-DD)
 * @param {string} dadosPagamento.forma_pagamento - Forma de pagamento
 * @param {string} dadosPagamento.observacoes - Observações sobre o pagamento
 * @returns {Object|false} - Dados da fatura atualizada ou false em caso de erro
 */
export async function marcarComoPaga(faturaId, dadosPagamento) {
    try {
        const response = await axios.post(`/faturas/${faturaId}/marcar-paga`, dadosPagamento);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data.data;

    } catch (error) {
        console.error('Erro ao marcar fatura como paga:', error);
    }

    return false;
}

/**
 * Cancelar fatura
 * @param {number} faturaId - ID da fatura
 * @param {string} motivo - Motivo do cancelamento
 * @returns {Object|false} - Dados da fatura atualizada ou false em caso de erro
 */
export async function cancelarFatura(faturaId, motivo) {
    try {
        const response = await axios.post(`/faturas/${faturaId}/cancelar`, { motivo });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response.data.data;

    } catch (error) {
        console.error('Erro ao cancelar fatura:', error);
    }

    return false;
}

/**
 * Função auxiliar para calcular datas de vencimento inteligentes
 * @param {string} dataInicial - Data inicial (YYYY-MM-DD)
 * @param {number} numeroParcelas - Número de parcelas
 * @returns {Array} - Array com as datas de vencimento
 */
export function calcularDataVencimentoInteligente(dataInicial, numeroParcelas) {
    const datas = [];
    const dataBase = new Date(dataInicial);
    const diaVencimento = dataBase.getDate();

    for (let i = 0; i < numeroParcelas; i++) {
        const novaData = new Date(dataBase.getFullYear(), dataBase.getMonth() + i, 1);
        
        // Calcular o último dia do mês
        const ultimoDiaDoMes = new Date(novaData.getFullYear(), novaData.getMonth() + 1, 0).getDate();
        
        // Se o dia de vencimento é maior que o último dia do mês, usar o último dia
        const diaFinal = Math.min(diaVencimento, ultimoDiaDoMes);
        
        novaData.setDate(diaFinal);
        datas.push(novaData.toISOString().split('T')[0]);
    }

    return datas;
}

/**
 * Função auxiliar para formatar valor monetário
 * @param {number} valor - Valor numérico
 * @returns {string} - Valor formatado em reais
 */
export function formatarValor(valor) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(valor);
}

/**
 * Função auxiliar para formatar data
 * @param {string} data - Data no formato YYYY-MM-DD
 * @returns {string} - Data formatada em DD/MM/YYYY
 */
export function formatarData(data) {
    if (!data) return '';
    const [ano, mes, dia] = data.split('-');
    return `${dia}/${mes}/${ano}`;
}

/**
 * Função auxiliar para obter status da fatura com cor
 * @param {string} status - Status da fatura
 * @returns {Object} - Objeto com label e classe CSS
 */
export function getStatusFatura(status) {
    const statusMap = {
        'pendente': { label: 'Pendente', class: 'bg-warning' },
        'paga': { label: 'Paga', class: 'bg-success' },
        'vencida': { label: 'Vencida', class: 'bg-danger' },
        'cancelada': { label: 'Cancelada', class: 'bg-secondary' },
        'parcial': { label: 'Pago Parcial', class: 'bg-info' }
    };

    return statusMap[status] || { label: 'Desconhecido', class: 'bg-dark' };
}
